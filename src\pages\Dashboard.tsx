import { 
  BarChart4, 
  DollarSign, 
  Package, 
  ShoppingCart, 
  Users, 
  TrendingUp, 
  TrendingDown,
  ArrowRight,
  Calendar
} from "lucide-react";
import { MetricCard } from "@/components/common/MetricCard";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { DataTable } from "@/components/common/DataTable";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getFirestore, collection, getDocs, query, where, orderBy, limit, Timestamp } from "firebase/firestore";
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isToday, subDays, parseISO } from "date-fns";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Sale, Product } from "@/types/firebase";

export default function Dashboard() {
  const { isAdmin, currentUser } = useAuth();
  const [salesData, setSalesData] = useState<{ day: string; sales: number }[]>([]);
  
  const { data: allSales = [], isLoading: isLoadingSales } = useQuery({
    queryKey: ["sales"],
    queryFn: async () => {
      try {
        const db = getFirestore();
        const salesCollection = collection(db, "sales");
        const querySnapshot = await getDocs(salesCollection);
        
        const sales = querySnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            date: data.date || (data.timestamp ? format(new Date(data.timestamp.seconds * 1000), 'yyyy-MM-dd') : ''),
            amount: data.amount || 0,
            items: data.items || 0,
            customer: data.customer || 'Unknown',
            status: data.status || 'completed'
          };
        });
        
        console.log("Fetched sales data:", sales);
        return sales as Sale[];
      } catch (error) {
        console.error("Error fetching sales:", error);
        toast.error("Failed to load sales data");
        return [];
      }
    }
  });

  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products"],
    queryFn: async () => {
      try {
        const db = getFirestore();
        const productsCollection = collection(db, "products");
        const querySnapshot = await getDocs(productsCollection);
        
        const products = querySnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            name: data.name || 'Unnamed Product',
            category: data.category || 'Uncategorized',
            stock: data.stock || 0,
            sku: data.sku || '',
            threshold: data.threshold || 5,
            price: data.price || 0
          };
        });
        
        console.log("Fetched products data:", products);
        return products as Product[];
      } catch (error) {
        console.error("Error fetching products:", error);
        toast.error("Failed to load product data");
        return [];
      }
    }
  });
  
  const recentSales = allSales && allSales.length > 0
    ? [...allSales]
        .sort((a, b) => {
          const dateA = a.timestamp 
            ? new Date(a.timestamp.seconds * 1000) 
            : (a.date ? new Date(a.date) : new Date(0));
          const dateB = b.timestamp 
            ? new Date(b.timestamp.seconds * 1000) 
            : (b.date ? new Date(b.date) : new Date(0));
          return dateB.getTime() - dateA.getTime();
        })
        .slice(0, 5)
    : [];
  
  const lowStockItems = products && products.length > 0
    ? products
        .filter(product => (product.stock <= (product.threshold || 5)))
        .sort((a, b) => a.stock - b.stock)
        .slice(0, 5)
    : [];
  
  useEffect(() => {
    if (allSales && allSales.length > 0) {
      const last7Days = Array.from({ length: 7 }, (_, i) => subDays(new Date(), 6 - i));
      
      const formattedData = last7Days.map(date => ({
        day: format(date, 'EEE'),
        sales: 0,
        fullDate: date
      }));
      
      allSales.forEach(sale => {
        let saleDate;
        if (sale.timestamp && sale.timestamp.seconds) {
          saleDate = new Date(sale.timestamp.seconds * 1000);
        } else if (sale.date) {
          try {
            saleDate = new Date(sale.date);
            if (isNaN(saleDate.getTime())) {
              return;
            }
          } catch (error) {
            console.error("Invalid date format:", sale.date);
            return;
          }
        } else {
          return;
        }
        
        const dayIndex = formattedData.findIndex(item => 
          format(item.fullDate, 'yyyy-MM-dd') === format(saleDate, 'yyyy-MM-dd')
        );
        
        if (dayIndex !== -1) {
          formattedData[dayIndex].sales += sale.amount || 0;
        }
      });
      
      setSalesData(formattedData.map(({ day, sales }) => ({ day, sales })));
    }
  }, [allSales]);
  
  const getTodaySales = () => {
    let totalAmount = 0;
    let count = 0;
    
    if (!allSales || allSales.length === 0) return { amount: 0, count: 0 };
    
    allSales.forEach(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return;
        }
      } else {
        return;
      }
      
      if (isToday(saleDate)) {
        totalAmount += (sale.amount || 0);
        count++;
      }
    });
    
    return { amount: totalAmount, count };
  };
  
  const getLowStockCount = () => {
    return products ? products.filter(product => product.stock <= (product.threshold || 5)).length : 0;
  };
  
  const getPendingOrders = () => {
    return allSales ? allSales.filter(sale => sale.status === 'pending').length : 0;
  };
  
  const getTodayCustomers = () => {
    if (!allSales || allSales.length === 0) return 0;
    
    const todayCustomers = new Set();
    
    allSales.forEach(sale => {
      let saleDate;
      if (sale.timestamp && sale.timestamp.seconds) {
        saleDate = new Date(sale.timestamp.seconds * 1000);
      } else if (sale.date) {
        try {
          saleDate = new Date(sale.date);
          if (isNaN(saleDate.getTime())) {
            return;
          }
        } catch (error) {
          console.error("Invalid date format:", sale.date);
          return;
        }
      } else {
        return;
      }
      
      if (isToday(saleDate) && sale.customer) {
        todayCustomers.add(sale.customer);
      }
    });
    
    return todayCustomers.size;
  };
  
  const todaySales = getTodaySales();
  const lowStockCount = getLowStockCount();
  const pendingOrders = getPendingOrders();
  const todayCustomers = getTodayCustomers();
  
  const adminMetrics = [
    { title: "Total Revenue", value: `$${allSales ? allSales.reduce((sum, sale) => sum + (sale.amount || 0), 0).toFixed(2) : "0.00"}`, description: "This month", icon: <DollarSign />, trend: { value: 12, positive: true } },
    { title: "Sales", value: allSales ? allSales.length.toString() : "0", description: "This week", icon: <ShoppingCart />, trend: { value: 8, positive: true } },
    { title: "Inventory", value: products ? products.length.toString() : "0", description: "Active products", icon: <Package />, trend: { value: 2, positive: false } },
    { title: "Customers", value: allSales ? Array.from(new Set(allSales.map(sale => sale.customer))).length.toString() : "0", description: "Active accounts", icon: <Users />, trend: { value: 5, positive: true } },
  ];

  const shopkeeperMetrics = [
    { title: "Today's Sales", value: `$${todaySales.amount.toFixed(2)}`, description: `${todaySales.count} transactions`, icon: <ShoppingCart />, trend: { value: 12, positive: true } },
    { title: "Low Stock Items", value: lowStockCount.toString(), description: "Need restocking", icon: <Package />, trend: { value: 8, positive: false } },
    { title: "Pending Orders", value: pendingOrders.toString(), description: "To be processed", icon: <ShoppingCart /> },
    { title: "Today's Customers", value: todayCustomers.toString(), description: "In-store visitors", icon: <Users />, trend: { value: 5, positive: true } },
  ];
  
  const metrics = isAdmin() ? adminMetrics : shopkeeperMetrics;

  const totalSales = salesData.reduce((sum, day) => sum + day.sales, 0);
  const hasWeeklyChange = salesData.length > 0;

  const formatDateForDisplay = (sale: any) => {
    if (!sale) return 'Unknown';
    
    if (sale.timestamp && sale.timestamp.seconds) {
      return format(new Date(sale.timestamp.seconds * 1000), 'yyyy-MM-dd');
    } else if (sale.date) {
      try {
        return format(new Date(sale.date), 'yyyy-MM-dd');
      } catch (error) {
        console.error("Invalid date format:", sale.date);
        return sale.date || 'Unknown';
      }
    }
    
    return 'Unknown';
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{isAdmin() ? 'Admin Dashboard' : 'Shop Dashboard'}</h1>
        <p className="text-muted-foreground">
          Welcome to your {isAdmin() ? 'admin' : 'shop'} dashboard, here's what's happening today.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            description={metric.description}
            icon={metric.icon}
            trend={metric.trend}
            className="scale-in"
            style={{ animationDelay: `${index * 100}ms` }}
          />
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="lg:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Sales Overview</CardTitle>
              <CardDescription>Daily sales for the last week</CardDescription>
            </div>
            {isAdmin() && (
              <Button variant="outline" size="sm" asChild>
                <Link to="/reports">
                  View Reports
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            )}
          </CardHeader>
          <CardContent className="pt-4">
            <div className="h-[300px]">
              {isLoadingSales ? (
                <div className="flex items-center justify-center h-full">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : salesData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={salesData} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'white', 
                        borderRadius: '8px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        border: 'none',
                      }} 
                      formatter={(value) => [`$${value}`, 'Sales']}
                    />
                    <Bar dataKey="sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-muted-foreground">No sales data available</p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center text-sm text-muted-foreground">
                {hasWeeklyChange ? (
                  <>
                    <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
                    <span className="text-green-500 font-medium">+18%</span>
                    <span className="ml-1">from last week</span>
                  </>
                ) : (
                  <span>Not enough data</span>
                )}
              </div>
              <div className="text-sm font-medium">
                Total: ${totalSales.toFixed(2)}
              </div>
            </div>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and actions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {isLoadingSales || isLoadingProducts ? (
                <div className="flex justify-center py-4">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : (
                <>
                  {recentSales.length > 0 && (
                    <div className="flex items-center gap-3 rounded-lg border p-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-background">
                        <ShoppingCart className="h-4 w-4 text-green-500" />
                      </div>
                      <div className="text-sm">New sale completed - ${recentSales[0].amount?.toFixed(2)}</div>
                    </div>
                  )}
                  
                  {lowStockItems.length > 0 && (
                    <div className="flex items-center gap-3 rounded-lg border p-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-background">
                        <Package className="h-4 w-4 text-amber-500" />
                      </div>
                      <div className="text-sm">{lowStockItems[0].name} low in stock - {lowStockItems[0].stock} remaining</div>
                    </div>
                  )}
                  
                  {pendingOrders > 0 && (
                    <div className="flex items-center gap-3 rounded-lg border p-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-background">
                        <TrendingDown className="h-4 w-4 text-blue-500" />
                      </div>
                      <div className="text-sm">{pendingOrders} pending orders need processing</div>
                    </div>
                  )}
                  
                  {lowStockItems.filter(item => item.stock === 0).length > 0 && (
                    <div className="flex items-center gap-3 rounded-lg border p-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-background">
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      </div>
                      <div className="text-sm">{lowStockItems.filter(item => item.stock === 0).length} products out of stock</div>
                    </div>
                  )}
                  
                  {todaySales.count > 0 && (
                    <div className="flex items-center gap-3 rounded-lg border p-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-background">
                        <Users className="h-4 w-4 text-indigo-500" />
                      </div>
                      <div className="text-sm">{todayCustomers} customers visited today</div>
                    </div>
                  )}
                </>
              )}
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4">
            <Button variant="outline" size="sm" className="w-full">
              View All Activity
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recent Sales</CardTitle>
              <CardDescription>Latest sales transactions</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link to="/sales">
                View All
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <DataTable
              data={recentSales}
              columns={[
                {
                  id: "date",
                  header: "Date",
                  cell: (row: any) => {
                    return (
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>{formatDateForDisplay(row)}</span>
                      </div>
                    );
                  }
                },
                {
                  id: "customer",
                  header: "Customer",
                  cell: (row: any) => <div className="font-medium">{row.customer || 'Unknown'}</div>,
                },
                {
                  id: "amount",
                  header: "Amount",
                  cell: (row: any) => (
                    <div className="font-medium text-right">
                      ${row.amount?.toFixed(2) || '0.00'}
                    </div>
                  ),
                },
              ]}
              isLoading={isLoadingSales}
              emptyMessage="No recent sales"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Low Stock Items</CardTitle>
              <CardDescription>Items that need attention</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link to="/inventory">
                View Inventory
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <DataTable
              data={lowStockItems}
              columns={[
                {
                  id: "name",
                  header: "Product",
                  cell: (row: any) => <div className="font-medium">{row.name || 'Unnamed Product'}</div>,
                },
                {
                  id: "category",
                  header: "Category",
                  cell: (row: any) => <div>{row.category || 'Uncategorized'}</div>,
                },
                {
                  id: "stock",
                  header: "Stock",
                  cell: (row: any) => (
                    <div className={`font-medium ${row.stock === 0 ? 'text-red-500' : row.stock < (row.threshold || 5) ? 'text-amber-500' : ''}`}>
                      {row.stock || 0}
                    </div>
                  ),
                },
                {
                  id: "threshold",
                  header: "Threshold",
                  cell: (row: any) => <div>{row.threshold || 5}</div>,
                },
              ]}
              isLoading={isLoadingProducts}
              emptyMessage="No low stock items"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
