
import { useState } from "react";
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardD<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>Title 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/AuthContext";
import { 
  MessageSquare, 
  MessageCircle, 
  Bell, 
  Send, 
  User,
  Calendar,
  Info,
  Clock,
  Megaphone
} from "lucide-react";
import { getFirestore, collection, getDocs, addDoc, query, orderBy } from "firebase/firestore";

type Message = {
  id: string;
  content: string;
  sender: {
    id: string;
    name: string;
  };
  recipient: {
    id: string;
    name: string;
  };
  isRead: boolean;
  timestamp: string;
};

type Announcement = {
  id: string;
  title: string;
  content: string;
  author: string;
  timestamp: string;
  isActive: boolean;
};

export default function Messaging() {
  const [activeTab, setActiveTab] = useState("messages");
  const [newMessage, setNewMessage] = useState("");
  const [newAnnouncementTitle, setNewAnnouncementTitle] = useState("");
  const [newAnnouncementContent, setNewAnnouncementContent] = useState("");
  const [selectedRecipient, setSelectedRecipient] = useState("admin");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { currentUser, isAdmin } = useAuth();

  const { data: messages = [], isLoading: isLoadingMessages, refetch: refetchMessages } = useQuery({
    queryKey: ["messages"],
    queryFn: async () => {
      const db = getFirestore();
      const messagesCollection = collection(db, "messages");
      const messagesQuery = query(messagesCollection, orderBy("timestamp", "asc"));
      const snapshot = await getDocs(messagesQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Message, "id">)
      }));
    }
  });

  const { data: announcements = [], isLoading: isLoadingAnnouncements } = useQuery({
    queryKey: ["announcements"],
    queryFn: async () => {
      const db = getFirestore();
      const announcementsCollection = collection(db, "announcements");
      const announcementsQuery = query(announcementsCollection, orderBy("timestamp", "desc"));
      const snapshot = await getDocs(announcementsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Announcement, "id">)
      }));
    }
  });

  const { data: staffMembers = [], isLoading: isLoadingStaff } = useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const db = getFirestore();
      const usersCollection = collection(db, "users");
      const snapshot = await getDocs(usersCollection);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().displayName || doc.data().email?.split('@')[0] || 'Unknown',
        role: doc.data().role || 'Unknown',
        email: doc.data().email
      }));
    }
  });

  const handleSendMessage = async () => {
    if (!newMessage.trim()) {
      toast({
        title: "Error",
        description: "Message cannot be empty",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      const recipient = staffMembers.find(m => m.id === selectedRecipient);
      const db = getFirestore();
      
      await addDoc(collection(db, "messages"), {
        content: newMessage,
        sender: {
          id: currentUser?.uid || "shop",
          name: currentUser?.displayName || "Shop Staff",
        },
        recipient: {
          id: recipient?.id || "admin",
          name: recipient?.name || "Admin",
        },
        isRead: false,
        timestamp: new Date().toISOString(),
      });
      
      toast({
        title: "Success",
        description: "Message sent successfully",
      });
      
      setNewMessage("");
      refetchMessages();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to send message",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCreateAnnouncement = async () => {
    if (!newAnnouncementTitle.trim() || !newAnnouncementContent.trim()) {
      toast({
        title: "Error",
        description: "Please fill in both title and content",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const db = getFirestore();
      
      await addDoc(collection(db, "announcements"), {
        title: newAnnouncementTitle,
        content: newAnnouncementContent,
        author: currentUser?.displayName || "Shop Staff",
        timestamp: new Date().toISOString(),
        isActive: true,
      });
      
      toast({
        title: "Success",
        description: "Announcement created successfully",
      });
      
      setNewAnnouncementTitle("");
      setNewAnnouncementContent("");
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create announcement",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Invalid date";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Invalid date";
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      return "Invalid date";
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Messaging & Announcements</h1>
        <p className="text-muted-foreground">
          Communicate with team members and manage store announcements.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="messages">
            <MessageCircle className="mr-2 h-4 w-4" />
            Messages
          </TabsTrigger>
          <TabsTrigger value="announcements">
            <Megaphone className="mr-2 h-4 w-4" />
            Announcements
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="messages" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Contacts</CardTitle>
                  <CardDescription>Select a team member to message</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <div>
                    {isLoadingStaff ? (
                      <div className="flex justify-center p-4">
                        <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                      </div>
                    ) : (
                      <div className="divide-y">
                        {staffMembers.map((member) => (
                          <button
                            key={member.id}
                            className={`w-full flex items-center p-3 text-left hover:bg-muted/50 ${
                              selectedRecipient === member.id ? 'bg-muted' : ''
                            }`}
                            onClick={() => setSelectedRecipient(member.id)}
                          >
                            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 text-primary mr-3">
                              <User className="h-5 w-5" />
                            </div>
                            <div>
                              <div className="font-medium">{member.name}</div>
                              <div className="text-xs text-muted-foreground">{member.role}</div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="col-span-2">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <CardTitle>
                    {staffMembers.find((member) => member.id === selectedRecipient)?.name || "Select Recipient"}
                  </CardTitle>
                  <CardDescription>
                    Send messages to coordinate with your team
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-1 overflow-y-auto max-h-[400px]">
                  {isLoadingMessages ? (
                    <div className="flex justify-center p-4">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    </div>
                  ) : messages.filter((m: Message) => 
                      (m.sender?.id === selectedRecipient || m.recipient?.id === selectedRecipient) &&
                      (m.sender?.id === (currentUser?.uid || "shop") || m.recipient?.id === (currentUser?.uid || "shop"))
                    ).length === 0 ? (
                    <div className="flex flex-col items-center justify-center p-8 text-center text-muted-foreground">
                      <MessageSquare className="h-10 w-10 mb-2" />
                      <p>No messages yet</p>
                      <p className="text-sm">Start the conversation by sending a message</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.filter((m: Message) => 
                        (m.sender?.id === selectedRecipient || m.recipient?.id === selectedRecipient) &&
                        (m.sender?.id === (currentUser?.uid || "shop") || m.recipient?.id === (currentUser?.uid || "shop"))
                      ).sort((a: Message, b: Message) => 
                        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
                      ).map((message: Message) => {
                        const isSentByMe = message.sender?.id === (currentUser?.uid || "shop");
                        
                        return (
                          <div
                            key={message.id}
                            className={`flex ${isSentByMe ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-[80%] rounded-lg p-3 ${
                                isSentByMe
                                  ? 'bg-primary text-primary-foreground'
                                  : 'bg-muted'
                              }`}
                            >
                              <div className="space-y-1">
                                <p>{message.content}</p>
                                <p className={`text-xs ${isSentByMe ? 'text-primary-foreground/70' : 'text-muted-foreground'}`}>
                                  {formatDate(message.timestamp)}
                                </p>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="border-t pt-4">
                  <div className="flex w-full items-center space-x-2">
                    <Textarea
                      placeholder="Type your message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="flex-1"
                      rows={2}
                    />
                    <Button 
                      onClick={handleSendMessage} 
                      disabled={isSubmitting || !selectedRecipient || !newMessage.trim()}
                    >
                      {isSubmitting ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="announcements" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Store Announcements</CardTitle>
                  <CardDescription>Recent announcements for the team</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingAnnouncements ? (
                    <div className="flex justify-center p-4">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    </div>
                  ) : !announcements.length ? (
                    <div className="flex flex-col items-center justify-center p-8 text-center text-muted-foreground">
                      <Bell className="h-10 w-10 mb-2" />
                      <p>No announcements yet</p>
                      <p className="text-sm">Create a new announcement to keep your team informed</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {announcements.map((announcement: Announcement) => (
                        <div key={announcement.id} className="rounded-lg border p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold">{announcement.title}</h3>
                            <Badge variant={announcement.isActive ? "default" : "outline"}>
                              {announcement.isActive ? "Active" : "Archived"}
                            </Badge>
                          </div>
                          <div className="text-sm mb-2">{announcement.content}</div>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <User className="h-3 w-3 mr-1" />
                            <span className="mr-3">{announcement.author}</span>
                            <Calendar className="h-3 w-3 mr-1" />
                            <span>{formatDate(announcement.timestamp)}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
            
            {isAdmin() && (
              <div className="col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Create Announcement</CardTitle>
                    <CardDescription>Post a new announcement for the team</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Title</Label>
                      <Input
                        id="title"
                        value={newAnnouncementTitle}
                        onChange={(e) => setNewAnnouncementTitle(e.target.value)}
                        placeholder="Announcement title"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        value={newAnnouncementContent}
                        onChange={(e) => setNewAnnouncementContent(e.target.value)}
                        placeholder="Announcement details..."
                        rows={4}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button 
                      onClick={handleCreateAnnouncement}
                      disabled={isSubmitting || !newAnnouncementTitle.trim() || !newAnnouncementContent.trim()}
                    >
                      {isSubmitting ? (
                        <>
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                          Posting...
                        </>
                      ) : (
                        <>
                          <Megaphone className="mr-2 h-4 w-4" />
                          Post Announcement
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            )}
            
            {!isAdmin() && (
              <div className="col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Announcements Info</CardTitle>
                    <CardDescription>View announcements from management</CardDescription>
                  </CardHeader>
                  <CardContent className="flex flex-col items-center justify-center text-center p-6 space-y-4">
                    <Info className="h-12 w-12 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Announcements are Read-Only</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Only administrators can create and manage announcements for the team
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
