import { useState, useEffect } from "react";
import { 
  MoreHorizontal, 
  Plus, 
  Search, 
  UserPlus, 
  Users 
} from "lucide-react";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTable } from "@/components/common/DataTable";
import { useAuth } from "@/context/AuthContext";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { createUserWithEmailAndPassword, getAuth } from "firebase/auth";
import { firestore, firebaseFirestore } from "@/lib/firebase";
import { doc, setDoc } from "firebase/firestore";
import type { FirestoreUser } from "@/types/firebase";

type Role = "admin" | "shopkeeper";
const roleOptions: Role[] = ["admin", "shopkeeper"];

interface StaffMember {
  id: string;
  name: string;
  role: Role;
  email: string | null;
  status: string;
}

const useStaffData = () => {
  const [staffData, setStaffData] = useState<StaffMember[]>([]);

  useEffect(() => {
    const unsubscribe = firestore.collection("users").onSnapshot((users) => {
      const userData = users.map(user => ({
        id: user.id,
        name: user.displayName || user.email?.split("@")[0] || "Unknown",
        role: (user.role || "shopkeeper") as Role,
        email: user.email,
        status: user.status || "active"
      }));
      setStaffData(userData);
    });

    return () => unsubscribe();
  }, []);

  return staffData;
};

interface NewStaffForm {
  name: string;
  email: string;
  role: Role | "";
  password: string;
}

export default function Staff() {
  const { isAdmin } = useAuth();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddStaffOpen, setIsAddStaffOpen] = useState(false);
  const [newStaff, setNewStaff] = useState<NewStaffForm>({
    name: "",
    email: "",
    role: "",
    password: "",
  });

  const staffData = useStaffData();
  
  // Metrics calculations
  const totalStaff = staffData.length;
  const activeStaff = staffData.filter(staff => staff.status === 'active').length;

  const handleAddStaff = async () => {
    try {
      // Validate form
      if (!newStaff.name || !newStaff.email || !newStaff.role || !newStaff.password) {
        toast({
          title: "Missing Information",
          description: "Please fill in all fields to add a new staff member.",
          variant: "destructive",
        });
        return;
      }

      // Create Firebase Auth user
      const auth = getAuth();
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        newStaff.email,
        newStaff.password
      );

      // Create Firestore user document
      await setDoc(doc(firebaseFirestore, "users", userCredential.user.uid), {
        email: newStaff.email,
        displayName: newStaff.name,
        role: newStaff.role,
        status: "active",
        createdAt: new Date(),
      });

      toast({
        title: "Staff Added",
        description: `${newStaff.name} has been added as ${newStaff.role}.`,
      });

      // Reset form and close dialog
      setNewStaff({
        name: "",
        email: "",
        role: "",
        password: "",
      });
      setIsAddStaffOpen(false);
    } catch (error) {
      console.error("Error adding staff:", error);
      toast({
        title: "Error",
        description: "Failed to add staff member. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Staff Management</h1>
        <p className="text-muted-foreground">
          Monitor and manage your store staff.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStaff}</div>
            <p className="text-xs text-muted-foreground">
              {activeStaff} currently active
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search staff..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {isAdmin() && (
            <Button onClick={() => setIsAddStaffOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Staff
            </Button>
          )}
        </div>

        <DataTable
          data={staffData}
          columns={[
            {
              id: "name",
              header: "Name",
              cell: (row) => <div className="font-medium">{row.name}</div>,
            },
            {
              id: "role",
              header: "Role",
              cell: (row) => <div>{row.role}</div>,
            },
            {
              id: "email",
              header: "Email",
              cell: (row) => <div>{row.email}</div>,
            },
            {
              id: "status",
              header: "Status",
              cell: (row) => (
                <div className={`px-2 py-1 rounded-full text-xs font-medium inline-block ${
                  row.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
                </div>
              ),
            },
            {
              id: "actions",
              header: "",
              cell: (row) => (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>View Details</DropdownMenuItem>
                    <DropdownMenuItem>Edit</DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">Deactivate</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ),
            },
          ]}
        />
      </div>

      <Dialog open={isAddStaffOpen} onOpenChange={setIsAddStaffOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Staff Member</DialogTitle>
            <DialogDescription>
              Create a new authenticated account for staff member.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newStaff.name}
                onChange={(e) => setNewStaff({ ...newStaff, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={newStaff.email}
                onChange={(e) => setNewStaff({ ...newStaff, email: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Role
              </Label>
              <div className="col-span-3">
                <select
                  id="role"
                  value={newStaff.role}
                  onChange={(e) => setNewStaff({ ...newStaff, role: e.target.value as Role })}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="" disabled>Select Role</option>
                  {roleOptions.map((role) => (
                    <option key={role} value={role}>{role}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Password
              </Label>
              <Input
                id="password"
                type="password"
                value={newStaff.password}
                onChange={(e) => setNewStaff({ ...newStaff, password: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddStaffOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddStaff}>Add Staff</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
