
import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  Card<PERSON><PERSON><PERSON>,
  CardFooter
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>ist, 
  Ta<PERSON>Trigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/context/AuthContext";
import { 
  User,
  Building, 
  Shield,
  Save,
  RefreshCw,
  Palette
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Toggle } from "@/components/ui/toggle";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { firestore } from "@/lib/firebase";

export default function Settings() {
  const [activeTab, setActiveTab] = useState("profile");
  const { currentUser, isAdmin } = useAuth();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [userName, setUserName] = useState(currentUser?.displayName || "");
  const [theme, setTheme] = useState("light");
  
  // Fetch current theme
  useEffect(() => {
    // Check if theme is stored in localStorage
    const storedTheme = localStorage.getItem("theme");
    if (storedTheme) {
      setTheme(storedTheme);
      document.documentElement.classList.toggle("dark", storedTheme === "dark");
    }
  }, []);

  const handleChangeTheme = (value: string) => {
    if (value) {
      setTheme(value);
      localStorage.setItem("theme", value);
      document.documentElement.classList.toggle("dark", value === "dark");
    }
  };

  const handleSaveProfile = () => {
    setIsSaving(true);
    
    // Update profile in Firebase
    if (currentUser) {
      firestore.collection("users").doc(currentUser.uid).update({
        displayName: userName
      })
      .then(() => {
        setIsSaving(false);
        toast({
          title: "Profile updated",
          description: "Your profile has been successfully updated.",
        });
      })
      .catch((error) => {
        console.error("Error updating profile:", error);
        setIsSaving(false);
        toast({
          title: "Update failed",
          description: "There was an error updating your profile.",
          variant: "destructive",
        });
      });
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your application settings and preferences.
          </p>
        </div>
        {isAdmin() && (
          <Badge variant="secondary" className="px-3 py-1">
            Admin Access
          </Badge>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-3">
          <TabsTrigger value="profile">
            <User className="mr-2 h-4 w-4" />
            Profile
          </TabsTrigger>
          
          {isAdmin() && (
            <TabsTrigger value="business">
              <Building className="mr-2 h-4 w-4" />
              Business
            </TabsTrigger>
          )}
          
          <TabsTrigger value="theme">
            <Palette className="mr-2 h-4 w-4" />
            Theme
          </TabsTrigger>
          
          {isAdmin() && (
            <TabsTrigger value="security">
              <Shield className="mr-2 h-4 w-4" />
              Security
            </TabsTrigger>
          )}
        </TabsList>
        
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile Settings</CardTitle>
              <CardDescription>Manage your personal information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input 
                  id="name" 
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="Your name" 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input 
                  id="email" 
                  value={currentUser?.email || ""} 
                  placeholder="Your email" 
                  type="email" 
                  disabled 
                />
                <p className="text-sm text-muted-foreground">Email address cannot be changed</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Input id="role" value={isAdmin() ? "Administrator" : "Shopkeeper"} disabled />
                <p className="text-sm text-muted-foreground">Your role determines your permissions in the system</p>
              </div>
            </CardContent>
            <CardFooter className="justify-end border-t px-6 py-4">
              <Button onClick={handleSaveProfile} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {isAdmin() && (
          <TabsContent value="business">
            <Card>
              <CardHeader>
                <CardTitle>Business Settings</CardTitle>
                <CardDescription>Manage your store and business information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="business-name">Business Name</Label>
                  <Input id="business-name" defaultValue="My Shop" placeholder="Your business name" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="business-address">Business Address</Label>
                  <Textarea id="business-address" placeholder="Your business address" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="tax-id">Tax ID / Registration Number</Label>
                    <Input id="tax-id" placeholder="Your tax ID" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Default Currency</Label>
                    <Input id="currency" defaultValue="Tsh" disabled />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="business-hours">Business Hours</Label>
                  <Textarea 
                    id="business-hours" 
                    placeholder="Monday - Friday: 9AM - 6PM&#10;Saturday: 10AM - 4PM&#10;Sunday: Closed"
                    defaultValue="Monday - Friday: 9AM - 6PM&#10;Saturday: 10AM - 4PM&#10;Sunday: Closed"
                  />
                </div>
              </CardContent>
              <CardFooter className="justify-end border-t px-6 py-4">
                <Button onClick={() => {
                  toast({
                    title: "Settings saved",
                    description: "Your business settings have been successfully updated.",
                  });
                }}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        )}
        
        <TabsContent value="theme">
          <Card>
            <CardHeader>
              <CardTitle>Theme Settings</CardTitle>
              <CardDescription>Customize the application's appearance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Color Theme</Label>
                <div className="flex flex-col space-y-4">
                  <ToggleGroup 
                    type="single" 
                    value={theme}
                    onValueChange={handleChangeTheme}
                    className="justify-start"
                  >
                    <ToggleGroupItem value="light" className="flex gap-2 items-center">
                      <div className="w-5 h-5 rounded-full bg-white border border-gray-300"></div>
                      Light
                    </ToggleGroupItem>
                    <ToggleGroupItem value="dark" className="flex gap-2 items-center">
                      <div className="w-5 h-5 rounded-full bg-gray-800 border border-gray-600"></div>
                      Dark
                    </ToggleGroupItem>
                  </ToggleGroup>
                  
                  <div className="pt-4">
                    <p className="text-sm font-medium mb-2">Preview</p>
                    <div className={`p-4 rounded-md border ${theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-black'}`}>
                      <p className="font-medium">This is how your application will look</p>
                      <p className="text-sm mt-1 opacity-80">Sample text with {theme} theme applied</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2 pt-4 border-t">
                <Label>Sidebar Behavior</Label>
                <div className="flex items-center gap-2">
                  <Toggle>
                    Auto-hide sidebar on small screens
                  </Toggle>
                </div>
              </div>
            </CardContent>
            <CardFooter className="justify-end border-t px-6 py-4">
              <Button onClick={() => {
                // Already saved to localStorage in handleChangeTheme
                toast({
                  title: "Theme updated",
                  description: `The application theme has been set to ${theme}.`,
                });
              }}>
                <Save className="mr-2 h-4 w-4" />
                Save Theme
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {isAdmin() && (
          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>Manage your security and privacy settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="current-password">Current Password</Label>
                  <Input id="current-password" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-password">New Password</Label>
                  <Input id="new-password" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm New Password</Label>
                  <Input id="confirm-password" type="password" />
                </div>
                
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Two-Factor Authentication</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Add an extra layer of security to your account
                  </p>
                  <Button variant="outline">
                    <Shield className="mr-2 h-4 w-4" />
                    Enable 2FA
                  </Button>
                </div>
                
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Session Management</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Manage your active sessions and devices
                  </p>
                  <Button variant="outline" className="text-destructive">
                    Sign Out All Devices
                  </Button>
                </div>
              </CardContent>
              <CardFooter className="justify-end border-t px-6 py-4">
                <Button onClick={() => {
                  toast({
                    title: "Security settings updated",
                    description: "Your security settings have been successfully updated.",
                  });
                }}>
                  <Save className="mr-2 h-4 w-4" />
                  Update Security Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
