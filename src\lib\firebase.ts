import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  onSnapshot,
  query,
  where,
  serverTimestamp,
  DocumentData
} from 'firebase/firestore';
import type { Product, FirestoreUser } from '@/types/firebase';

// Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyChykTKrpubd1jBQNeWcYDJt8yWGyir9FM",
  authDomain: "newyok-9b743.firebaseapp.com",
  projectId: "newyok-9b743",
  storageBucket: "newyok-9b743.appspot.com",
  messagingSenderId: "119737226430",
  appId: "1:119737226430:web:30a1fe31d94d6dae1c7aef",
  measurementId: "G-RZ4ZMXG2QT"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const firebaseAuth = getAuth(app);
export const firebaseFirestore = getFirestore(app); // Added export

// Auth service
export const auth = {
  currentUser: firebaseAuth.currentUser,
  async signIn(email: string, password: string) {
    try {
      const userCredential = await signInWithEmailAndPassword(firebaseAuth, email, password);
      const userDoc = await getDoc(doc(firebaseFirestore, 'users', userCredential.user.uid));
      const userData = userDoc.exists() ? userDoc.data() as FirestoreUser : null;
      
      return { 
        user: { 
          uid: userCredential.user.uid, 
          email: userCredential.user.email, 
          displayName: userCredential.user.displayName || userData?.displayName || email.split('@')[0],
          role: userData?.role || 'shopkeeper'
        } 
      };
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    }
  },
  async signOut() {
    try {
      await firebaseSignOut(firebaseAuth);
      return true;
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  },
  onAuthStateChanged,
};

// Firestore service
export const firestore = {
  collection: (name: string) => ({
    get: async () => {
      try {
        const querySnapshot = await getDocs(collection(firebaseFirestore, name));
        return {
          docs: querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })),
        };
      } catch (error) {
        console.error(`Error getting collection ${name}:`, error);
        throw error;
      }
    },
    add: async (data: DocumentData) => {
      try {
        const docRef = await addDoc(collection(firebaseFirestore, name), {
          ...data,
          createdAt: serverTimestamp(),
        });
        return { id: docRef.id };
      } catch (error) {
        console.error(`Error adding document to ${name}:`, error);
        throw error;
      }
    },
    doc: (id: string) => ({
      get: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          const docSnap = await getDoc(docRef);
          return {
            exists: docSnap.exists(),
            data: () => ({ id: docSnap.id, ...docSnap.data() }),
          };
        } catch (error) {
          console.error(`Error getting document ${name}/${id}:`, error);
          throw error;
        }
      },
      update: async (data: DocumentData) => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await updateDoc(docRef, data);
          return true;
        } catch (error) {
          console.error(`Error updating document ${name}/${id}:`, error);
          throw error;
        }
      },
      delete: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await deleteDoc(docRef);
          return true;
        } catch (error) {
          console.error(`Error deleting document ${name}/${id}:`, error);
          throw error;
        }
      },
    }),
    onSnapshot: (callback: (data: DocumentData[]) => void) => {
      const q = collection(firebaseFirestore, name);
      return onSnapshot(q, (querySnapshot) => {
        const items = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        callback(items);
      });
    },
    where: (field: string, operator: any, value: any) => {
      const q = query(
        collection(firebaseFirestore, name),
        where(field, operator, value)
      );
      return {
        get: async () => {
          try {
            const querySnapshot = await getDocs(q);
            return {
              docs: querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              })),
            };
          } catch (error) {
            console.error(`Error querying collection ${name}:`, error);
            throw error;
          }
        },
        onSnapshot: (callback: (data: DocumentData[]) => void) => {
          return onSnapshot(q, (querySnapshot) => {
            const items = querySnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            callback(items);
          });
        }
      };
    }
  }),
};

// Create demo users in Firebase
export const createDemoData = async () => {
  try {
    // Check if we already have products
    const productsSnapshot = await getDocs(collection(firebaseFirestore, 'products'));
    if (productsSnapshot.empty) {
      const demoProducts = [
        { name: 'Product 1', price: 9.99, stock: 10, category: 'Category A', sku: 'SKU001', description: 'Product 1 description' },
        { name: 'Product 2', price: 19.99, stock: 5, category: 'Category B', sku: 'SKU002', description: 'Product 2 description' },
        { name: 'Product 3', price: 29.99, stock: 2, category: 'Category A', sku: 'SKU003', description: 'Product 3 description' },
        { name: 'Product 4', price: 39.99, stock: 0, category: 'Category C', sku: 'SKU004', description: 'Product 4 description' },
      ];
      
      for (const product of demoProducts) {
        await addDoc(collection(firebaseFirestore, 'products'), {
          ...product,
          createdAt: serverTimestamp(),
        });
      }
      console.log('Demo products created');
    }

    // Check if we already have users
    const usersSnapshot = await getDocs(collection(firebaseFirestore, 'users'));
    if (usersSnapshot.empty) {
      const demoUsers = [
        { email: '<EMAIL>', displayName: 'Admin User', role: 'admin' },
        { email: '<EMAIL>', displayName: 'Shop User', role: 'shopkeeper' },
      ];
      
      for (const user of demoUsers) {
        await addDoc(collection(firebaseFirestore, 'users'), {
          ...user,
          createdAt: serverTimestamp(),
        });
      }
      console.log('Demo user documents created');
    }
  } catch (error) {
    console.error('Error creating demo data:', error);
  }
};

// Initialize demo data
createDemoData();

export default { auth, firestore, createDemoData };